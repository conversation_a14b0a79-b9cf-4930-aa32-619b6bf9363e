const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugMessageData() {
  try {
    console.log('🔍 Debugging message data for thread_FZ8qiKPI5dGQjKQUTUp9j...')
    
    // Query the exact same way the API does
    const message = await prisma.aiMessage.findFirst({
      where: {
        conversationId: 'thread_FZ8qiKPI5dGQjKQUTUp9j',
        role: 'ASSISTANT'
      },
      select: {
        id: true,
        role: true,
        content: true,
        createdAt: true,
        steps: {
          select: {
            id: true,
            stepOrder: true,
            type: true,
            metadata: true,
            parallelKey: true,
            parentStepId: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: { stepOrder: 'asc' },
        },
        _count: {
          select: {
            steps: true,
          },
        },
      },
    })

    if (!message) {
      console.log('❌ No assistant message found')
      return
    }

    console.log('\n📊 Message Data:')
    console.log(`  ID: ${message.id}`)
    console.log(`  Role: ${message.role}`)
    console.log(`  Step Count (_count.steps): ${message._count.steps}`)
    console.log(`  Steps Array Length: ${message.steps?.length || 0}`)
    console.log(`  Content Preview: ${message.content.substring(0, 100)}...`)

    console.log('\n🔧 Step Breakdown:')
    const stepsByType = message.steps.reduce((acc, step) => {
      const key = step.type + (step.metadata?.toolName ? ` (${step.metadata.toolName})` : '')
      acc[key] = (acc[key] || 0) + 1
      return acc
    }, {})
    
    Object.entries(stepsByType).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`)
    })

    console.log('\n🔍 First 3 Tool Call Steps:')
    const toolCalls = message.steps.filter(step => 
      step.type === 'TOOL_CALL' && step.metadata?.toolName === 'web_search'
    )
    
    toolCalls.slice(0, 3).forEach((step, i) => {
      console.log(`  ${i + 1}. Step ${step.stepOrder}: "${step.metadata?.args?.query || 'N/A'}"`)
      console.log(`     Metadata keys: ${Object.keys(step.metadata || {}).join(', ')}`)
    })

    // Test the transformation that happens in the API
    console.log('\n🔄 API Transformation Test:')
    const includeSteps = true
    const transformed = {
      id: message.id,
      role: message.role,
      content: message.content,
      createdAt: message.createdAt,
      stepCount: message._count.steps,
      ...(includeSteps && message.steps && { steps: message.steps }),
    }

    console.log(`  Transformed stepCount: ${transformed.stepCount}`)
    console.log(`  Transformed has steps: ${!!transformed.steps}`)
    console.log(`  Transformed steps length: ${transformed.steps?.length || 0}`)

    // Check what would trigger hasSteps in the UI
    const hasSteps = transformed.role === 'ASSISTANT' && 
                     transformed.stepCount && 
                     transformed.stepCount > 0

    console.log(`  Would trigger hasSteps in UI: ${hasSteps}`)

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugMessageData()
