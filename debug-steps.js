const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function findConversationWithWebSearches() {
  try {
    console.log('🔍 Looking for conversations with web search tool calls...')

    // Find messages with web_search tool calls
    const messagesWithWebSearches = await prisma.aiMessage.findMany({
      where: {
        role: 'ASSISTANT',
        steps: {
          some: {
            type: 'TOOL_CALL',
            metadata: {
              path: ['toolName'],
              equals: 'web_search',
            },
          },
        },
      },
      include: {
        steps: {
          where: {
            type: {
              in: ['TOOL_CALL', 'TOOL_RESULT'],
            },
          },
          orderBy: {
            stepOrder: 'asc',
          },
        },
        conversation: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    })

    console.log(
      `Found ${messagesWithWebSearches.length} messages with web search tool calls`
    )

    messagesWithWebSearches.forEach((message, index) => {
      const webSearchSteps = message.steps.filter(
        step => step.metadata?.toolName === 'web_search'
      )

      console.log(`\nMessage ${index + 1}:`)
      console.log(`  ID: ${message.id}`)
      console.log(
        `  Conversation: ${message.conversation.id} (${message.conversation.title || 'Untitled'})`
      )
      console.log(`  Total steps: ${message.steps.length}`)
      console.log(`  Web search steps: ${webSearchSteps.length}`)
      console.log(`  Step breakdown:`)

      const stepsByType = message.steps.reduce((acc, step) => {
        const key =
          step.type +
          (step.metadata?.toolName ? ` (${step.metadata.toolName})` : '')
        acc[key] = (acc[key] || 0) + 1
        return acc
      }, {})

      Object.entries(stepsByType).forEach(([type, count]) => {
        console.log(`    ${type}: ${count}`)
      })

      // Show first few web search queries
      const toolCalls = message.steps.filter(
        step =>
          step.type === 'TOOL_CALL' && step.metadata?.toolName === 'web_search'
      )

      if (toolCalls.length > 0) {
        console.log(`  Sample queries:`)
        toolCalls.slice(0, 3).forEach((step, i) => {
          console.log(`    ${i + 1}. "${step.metadata?.args?.query || 'N/A'}"`)
        })
        if (toolCalls.length > 3) {
          console.log(`    ... and ${toolCalls.length - 3} more`)
        }
      }
    })

    // Find the one with exactly 8 web search tool calls
    const targetMessage = messagesWithWebSearches.find(message => {
      const webSearchToolCalls = message.steps.filter(
        step =>
          step.type === 'TOOL_CALL' && step.metadata?.toolName === 'web_search'
      )
      return webSearchToolCalls.length === 8
    })

    if (targetMessage) {
      console.log(`\n🎯 Found target message with 8 web searches:`)
      console.log(`   Message ID: ${targetMessage.id}`)
      console.log(`   Conversation ID: ${targetMessage.conversation.id}`)
      console.log(`   Conversation Title: ${targetMessage.conversation.title}`)

      // Get the context entity ID (dragTreeId) for this conversation
      const conversation = await prisma.aiConversation.findUnique({
        where: { id: targetMessage.conversation.id },
        select: { contextEntityId: true },
      })

      if (conversation?.contextEntityId) {
        console.log(`   DragTree ID: ${conversation.contextEntityId}`)
        console.log(
          `   URL: http://localhost:3000/dragTree/${conversation.contextEntityId}`
        )
        console.log(`\n💡 To debug:`)
        console.log(`   1. Open the URL above`)
        console.log(
          `   2. Click on the chat asset "${targetMessage.conversation.title}" in the sidebar`
        )
        console.log(`   3. Check browser console for debug logs`)
        console.log(
          `   4. Look for the ReasoningTimeline component and step data`
        )
      }
    } else {
      console.log(`\n❌ No message found with exactly 8 web search tool calls`)
    }
  } catch (error) {
    console.error('Error querying database:', error)
  } finally {
    await prisma.$disconnect()
  }
}

findConversationWithWebSearches()
