# Model Context Protocol (MCP) for **DragTree**

**Author:** Gemini Agent
**Last Revised:** 2025-07-16
**Status:** Proposed

---

## 1. Overview

The _Model Context Protocol_ (MCP) defines a minimal set of server-side tools and usage conventions that allow an LLM-powered agent to explore a user’s **DragTree** without loading the entire dataset into context. The goals are:

1. Minimise token cost through **lazy loading**.
2. Expose an **intuitive, discoverable** API surface for the model.
3. Integrate seamlessly with the existing Next 13 codebase—_no new micro-service_ required.

---

## 2. Core Principles

1. **Laziness** – The agent only fetches what it needs, when it needs it.
2. **Discoverability** – The tree structure is easy to traverse via children listings.
3. **Interactivity** – The agent can iterate: _summarise → drill-down → read content_.
4. **Token Efficiency** – All rich text is converted to Markdown before reaching the LLM.
5. **Single Source of Truth** – Data lives in Prisma tables already present.

---

## 3. MCP Tool Set

| Tool                               | Method & Endpoint                        | Description                                                        |
| ---------------------------------- | ---------------------------------------- | ------------------------------------------------------------------ |
| `getDragTreeSummary(drag_tree_id)` | `GET /api/drag-tree/{id}/summary`        | Returns a Markdown outline of root nodes.                          |
| `listChildNodes(parent_node_id)`   | `GET /api/drag-tree/nodes/{id}/children` | Lists direct children: `{ id, label, type, has_children }[]`.      |
| `getNodeContent(node_ids[])`       | `POST /api/drag-tree/get-content`        | Batch-fetches latest `DragTreeNodeContent`, converted to Markdown. |

All three endpoints authenticate via existing **NextAuth** session and are rate-limited with `libs/rateLimiter`.

---

## 4. Compatibility with the Current Codebase

### 4.1 Data-Model

- Tables `DragTree`, `DragTreeNode`, `DragTreeNodeContent` already exist – **no migration** needed.
- Parent/child links are stored in `drag_tree.tree_structure.hierarchy`, not as a foreign key. `listChildNodes` will therefore parse this JSON.

### 4.2 Reusable Helpers

| Concern                | Existing Utility                                          |
| ---------------------- | --------------------------------------------------------- |
| Lightweight tree fetch | `getDragTree(id,{ includeContentItems:false })`           |
| Batch content fetch    | `getNodeContentsBatch(contentIds)` (wrap by nodeId)       |
| Markdown conversion    | `convertTiptapJsonToMarkdown(json)`                       |
| Auth / rate-limit      | Standard helpers in `app/api/auth` and `libs/rateLimiter` |

### 4.3 Gaps to Fill

1. Summary formatter → `getDragTreeSummary`.
2. Child-listing helper that walks `tree_structure`.
3. Node-ID-based content fetcher that performs Markdown conversion.

---

## 5. Best-Practice Guidelines

1. Return **flat JSON**; avoid deeply nested structures.
2. Cap large responses at 100 items and include `has_more` + `next_cursor`.
3. Use deterministic IDs produced by `generateDragTreeNodeId()`.
4. Keep feature-flag checks consolidated in the UI layer.
5. Log every tool call for analytics and observability.

---

## 6. Implementation Roadmap

### Phase 1 — Core Tooling (≈ Week 1)

**Deliverables**

1. `lib/tiptapToMd.ts` – re-export markdown converter.
2. New server-actions: `getDragTreeSummary`, `listChildNodes`, `getNodeContent`.
3. Corresponding API routes with session & rate-limit guards.
4. Unit-test suite (Jest / Vitest).

**Checkpoints**

- [ ] Summary endpoint returns heading + root list for fixture tree.
- [ ] Child-listing returns correct children for mocked hierarchy.
- [ ] Content endpoint converts sample Tiptap JSON → expected Markdown.
- [ ] CI green.

**Unit-Test Snippets**

```ts
expect(await getDragTreeSummary('tree_1')).toMatch(/## Root Nodes/)

const kids = await listChildNodes('node_parent')
expect(kids).toEqual([
  { id: 'n1', label: 'Child 1', type: 'QUESTION', has_children: false },
])

const md = (await getNodeContent(['node_abc']))[0].content
expect(md).toContain('# Heading')
```

---

### Phase 2 — Chat-Agent Integration (≈ Week 2)

**Deliverables**

1. Extend LLM system-prompt with the three MCP tools.
2. Modify chat pipeline to:
   - auto-inject summary when a DragTree conversation starts,
   - pass subsequent tool calls to API routes.
3. Client-side stream handler for incremental tool responses.
4. Cache summaries in Edge KV/Redis.

**Checkpoints**

- [ ] Staging logs show agent issuing tool calls after initial summary.
- [ ] Chat UI renders streamed node content without full page refresh.
- [ ] p95 latency ≤ 1.5 s on tool endpoints.

**E2E Suggestions**

- Playwright: open DragTree chat → ensure summary appears → mock tool-call JSON → verify UI update.
- Integration: inject fake LLM response with tool-call → assert 200 & JSON schema.

---

### Phase 3 — Future Enhancements (optional)

- Cursor-based pagination for huge trees.
- Fine-grained sharing & permissions.
- Cross-tree search endpoint.

---

## 7. Open Questions

1. How to paginate `listChildNodes` for extremely large categories?
2. Edge cases in Tiptap-to-Markdown conversion (tables, Mermaid, etc.).
3. Should we expose batch _summary_ generation for a list of trees?
4. Telemetry format for tool-usage analytics.

---

_End of document._
