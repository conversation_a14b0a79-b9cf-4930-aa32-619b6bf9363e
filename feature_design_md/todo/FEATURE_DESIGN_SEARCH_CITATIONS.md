# Design Doc: Structured Search Results with Citations

**Author:** Gemini Agent (updated by o3 AI assistant)
**Date:** 2025-07-16
**Status:** Draft → **Reviewed / Updated**

---

## 1. Overview (unchanged)

This document outlines a plan to enhance the application's search functionality. Currently, search results from external tools like the Brave API are returned as plain text. The goal is to upgrade this to a structured data format, where the AI response includes both the formatted text and a machine-readable array of citations.

This will allow the application to:

- Reliably extract and store source information for annotations.
- Filter and display only the most relevant and cited sources.
- Create a richer user experience by linking inline citations to their sources.

The core of this feature will be implemented using the Vercel AI SDK's `generateObject` function, which allows us to define a response schema that the Large Language Model (LLM) must adhere to.

---

## 2. Technical Approach (updated)

### 2.1. Response Schema (no functional change)

```ts
import { z } from 'zod'

const citationSchema = z.object({
  id: z
    .number()
    .describe('Numeric ID matching the inline citation, e.g. 1 for «1».'),
  note: z.string().max(100).describe('≤ 12-word note on source relevance.'),
  url: z.string().url(),
  date: z.string(),
  publisher: z.string(),
})

export const structuredSearchResponseSchema = z.object({
  markdownResponse: z.string(),
  citations: z.array(citationSchema).optional(), // array *may* be empty
})
export type StructuredSearchResponse = z.infer<
  typeof structuredSearchResponseSchema
>
```

### 2.2. Drag-Tree Node `contentMetadata` Extension ⭐️ _NEW_

| Key             | Type                       | Notes                                                                             |
| --------------- | -------------------------- | --------------------------------------------------------------------------------- |
| `searchResults` | `SearchResult[]` (current) | Unchanged – still required for Brave / external search.                           |
| `citations`     | `Citation[]` (optional)    | Present **only** when the LLM produced inline «n» markers. Uses the schema above. |

- Back-end should always include `searchResults`.
- It **adds** `citations` only if at least one inline marker was generated.
- FE must treat both keys as optional when rendering to maintain backwards compatibility with existing nodes.

### 2.3. Prompt Routing Logic

1. **Internal “Quick Research”** → use the new, strict prompt (see Appendix) **+** `generateObject()` with schema.
2. **External service calls** → keep existing lightweight prompt and `streamText()` (no citations array).

### 2.4. Back-End Flow (Drag-Tree Quick Research)

1. Receive `POST /ai-chat/quick-research` with `{ message, nodeId }`.
2. If feature flag `ENABLE_STRUCTURED_CITATIONS` is **true**:
   a. Call LLM via `generateObject({ …, schema: structuredSearchResponseSchema })`.
   b. Persist `markdownResponse`, `citations` (if any) **and** Brave `searchResults` into `contentMetadata`.
3. Else (flag off): retain current behaviour.

**Performance guardrails**

- Do _not_ stream large Brave payloads to the FE. Persist whole result server-side, but send only `citations` and minimal `searchResults` to client.

---

## 3. Front-End Integration

### 3.1. Types

```ts
// app/types/ai-generation.ts (extend existing file)
export type Citation = {
  id: number
  note: string
  url: string
  date: string
  publisher: string
}

export type SearchResult = {
  /* existing shape */
}

export type NodeContentMetadata = {
  searchResults?: SearchResult[]
  citations?: Citation[] // NEW
}
```

### 3.2. Parsing Markdown & Inline Markers

- Re-use the existing markdown renderer.
- Add a remark / rehype plugin that _replaces_ every `«n»` with
  ```html
  <sup class="citation" data-id="n">[n]</sup>
  ```
  The plugin should emit a custom AST node so React can attach `onClick` and open the `SourceMenu` (already implemented in `SearchResultsDisplay.tsx`).

### 3.3. UI Updates

1. **`SearchResultsDisplay.tsx`**
   _No code breakage._ Existing `searchResults` prop remains.
   Add optional `citations` prop; if present, fall back to the **compact icon stack** already implemented for `searchResults` yet link the icon indices to `citation.id`.
2. **`SourceCitations`** component
   Accept either `searchResults` **or** `citations`. Prefer `citations` when both provided.
3. **Link Navigation**
   On click of a superscript `[n]`, open the existing `SourceMenu` with the corresponding citation highlighted.

---

## 4. Validation & Backwards Compatibility

| Scenario                            | Expected Behaviour                                      |
| ----------------------------------- | ------------------------------------------------------- |
| Old nodes with only `searchResults` | UI identical to current production.                     |
| New nodes with both keys            | Inline «n» markers render, popup shows **citations**.   |
| Quick-research disabled (flag off)  | Feature path untouched; app uses current simple prompt. |
| Large Brave payload                 | Only first 3 processed; others dropped server-side.     |

---

## 5. Roll-Out Plan

1. Hide behind `ENABLE_STRUCTURED_CITATIONS` env flag (default **false**).
2. Deploy to staging, migrate 3 existing drag-trees to new schema for smoke test.
3. Gradual user rollout (10 % → 50 % → 100 %).
4. Remove flag and legacy path once metrics stable for 1 week.

---

## 6. Open Questions

1. Should citations ever be persisted without `searchResults` to save space? No, keep both, but save network egress, only send useful things to FE
2. Retry strategy if `generateObject` fails schema validation? that should be handled by structural object that model provider deal with
3. UX for > 10 citations – scroll vs. paginate? scroll, since should not be many

---

## Appendix A – **Internal Prompt (Quick Research only)**

> **IMPORTANT** – Do _not_ expose this prompt to external requests.

### Core Objective & Guiding Principles

Your primary goal is to act as a **Senior Strategist**. Your task is to illuminate the strategic landscape surrounding the user's question, equipping them with a sophisticated and multi-dimensional understanding. The response must be built upon a clear, insightful analytical thesis, but its purpose is not just to persuade, but to guide the user's own critical thinking. The analysis must be reasoned, logical, and rigorously supported by evidence.

### Persona

Assume the role of a seasoned **Senior Strategist** or **Principal Analyst** briefing a sharp, intelligent decision-maker. Your tone is one of quiet confidence and deep expertise. You are not a salesperson; you are a trusted advisor whose value lies in clarity, context, and foresight.

---

### Response Structure & Rules

1.  **TL;DR**
    • **3 – 4 bullets**, ≤ 15 words each.
    • Synthesize the most critical takeaways from your analysis.
    • **Bold** every key number, term, or strategic concept.

2.  **Evidence Snapshot**
    • _Numeric Table_ or _Ranked Checklist_ (3-5 items).
    • Select credible data points or qualitative factors that provide essential context for the analysis.

3.  **Deep Dive**
    ► **Instruction:** This section is your primary analysis.
    1.  **Frame the Analysis (The "Clue"):** If the question is about a specific entity's internal state (e.g., "my company's problems," "my personal interests"), begin the `Deep Dive` with a brief, single sentence that frames the approach. This sentence should transparently state that since internal specifics are unknown, the analysis will proceed by applying proven frameworks and identifying the most probable, high-impact patterns seen across the relevant industry or field.
    2.  **Establish a Central Thesis:** Immediately following the framing sentence, distill your findings into a single, insightful analytical thesis that will serve as the guiding frame for your discussion.
    3.  **Construct a Fluid Narrative:** From there, write a single, uninterrupted narrative that explores the issue through the lens of your thesis by naturally addressing the issue's core **context**, the **critical questions** it raises, and its inherent **trade-offs or counterarguments**.

    ► **Crucial Execution Rule:** The Deep Dive must be a single piece of flowing prose. **You are explicitly forbidden from using subheadings, bullet points, or any other structural breaks within this section.** These analytical concepts must be woven seamlessly into your paragraphs to maintain a natural, expert tone and avoid a boilerplate feel. Use your **analytical palette** below to inform the content of your narrative.

    ► **Your Analytical Palette (Tools to inform your narrative):**
    • Quantitative Data
    • Historical Precedents / Case Studies
    • Strategic Frameworks
    • Exploration of Second-Order Effects

4.  **Actionable Takeaways**
    ► **Instruction:** Conclude with a clear, practical list of 3-5 strategic recommendations or next steps that logically follow from your Deep Dive analysis.

    ► **Crucial Execution Rule:** The Deep Dive must be a single piece of flowing prose. **You are explicitly forbidden from using subheadings, bullet points, or any other structural breaks within this section.** These analytical concepts must be woven seamlessly into your paragraphs to maintain a natural, expert tone and avoid a boilerplate feel. Use your **analytical palette** below to inform the content of your narrative.

    ► **Your Analytical Palette (Tools to inform your narrative):**
    • Quantitative Data
    • Historical Precedents / Case Studies
    • Strategic Frameworks
    • Exploration of Second-Order Effects

5.  **Actionable Takeaways**
    ► **Instruction:** Conclude with a clear, practical list of 3-5 strategic recommendations or next steps that logically follow from your Deep Dive analysis.

### Rules for Sources & Citations

• Place an inline citation marker «n» immediately after each sourced fact.
• Skip markers **only** for general concepts or hypothetical scenarios.
• After the Markdown block, output a JSON array named **citations** _only if markers appear_:
[ {id, note ≤ 12 w, url, date, publisher}, … ]
– Every inline ID **must** have a matching object, and vice-versa.
• **Source Hierarchy:** Prioritize (1) Peer-reviewed journals/govt data, (2) Reputable industry studies/surveys, (3) Top-tier financial or technology news outlets.
• **Validation Clause:** The answer is invalid unless all inline «n» markers match the IDs in the citations array exactly.
• Output _nothing_ except the Markdown block followed—if needed—by the raw JSON array.
This is the original user ask: "I just had a bad breakup, and I feel lost. What should I do"

Please generate the language in English
