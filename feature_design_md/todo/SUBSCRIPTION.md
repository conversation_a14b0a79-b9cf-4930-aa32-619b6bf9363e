# Design Doc: Subscription & Pricing v2 – **FREE ↔ PRO**

**Author:** o3 AI assistant (reviewing original draft by <PERSON>)
**Date:** 2025-07-16 _(updated)_
**Status:** Draft ➡️ **Reviewed / Actionable**

---

## 1. Why This Update?

The first draft correctly identified the need for a robust subscription system, but several **gaps** exist between the proposal and the live code:

1. `SubscriptionTier` enum in `prisma/schema.prisma` **lacks** the `PRO` value referenced throughout the doc.
2. Webhook logic **does not** listen for `customer.subscription.deleted`, leaving users permanently in a paid state if Stripe cancels early.
3. `isUserSubscribing()` (see `lib/utils.ts`) uses a **boolean** approach (`GUEST` + `subscription_end_date`) instead of the proposed tier model.
4. Front-end gating relies on `isSubscribed` only; no granular tier info is exposed to components.

This document closes those gaps and provides exact implementation steps so a coding agent can ship the feature safely.

---

## 2. Goals & Non-Goals

### Goals

1. Introduce a **clear two-tier model**: `FREE` vs `PRO` (paid).
2. Ensure **end-to-end correctness** across:
   • Database schema
   • Stripe webhooks
   • Server actions / helpers
   • React hooks + UI gating
3. Cover **edge cases** (failed payment, cancellation, grace period).
4. Provide **self-contained code snippets** & TODOs.

### Non-Goals

• Free-trial logic (out of scope).
• Non-Stripe payment processors (PayPal, etc.).

---

## 3. Schema Changes (🔨 _Migration Required_)

```prisma
// prisma/schema.prisma

enum SubscriptionTier {
  FREE      // default for all sign-ups
  PRO       // paying customers
  GUEST     // **friends & testers** → same runtime permissions as PRO
  ULTRA     // reserved (higher usage limits)
  BUSINESS  // reserved (multi-seat, SLA)
}

/*
 NOTE
 -----
 – ULTRA / BUSINESS are **place-holders only**.
 – They allow forward-compatible migration without another breaking enum change.
 – Stripe Price IDs should encode the tier they belong to (e.g. `pro_monthly`, `ultra_yearly`).
*/

model User {
  …
  subscription_cancel_pending Boolean @default(false)
}
```

**Migration strategy**

1. **Do not** drop `GUEST` yet; other functions still reference it (see Section 7).

---

## 4. Back-End Flow Re-Audit

Below is the ideal lifecycle with required code touches in _bold italics_.

| Stripe Event                    | Handler (file)                                                                                   | Key Updates                                                                                                                  |
| ------------------------------- | ------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------- |
| `checkout.session.completed`    | `webhook/utils.ts ➜ handleCheckoutSessionCompleted()`                                            | Already creates `Subscription` row. ✅                                                                                       |
| `customer.subscription.created` | `manageSubscriptionRecord()`                                                                     | Needs to set **`subscription_tier = PRO`** on `User`. 🆕                                                                     |
| `customer.subscription.updated` | `manageSubscriptionRecord()`                                                                     | Keep tier `PRO`. If `cancel_at_period_end == true`, mark `subscription_cancel_pending = true` (new column or user-metadata). |
| `customer.subscription.deleted` | **ADD HANDLER** → downgrade `User.subscription_tier = FREE`, nullify `subscription_end_date`. 🆕 |
| `invoice.payment_failed`        | (optional) **ADD** → send email + display banner; no tier change until Stripe actually cancels.  |

### 4.1. Helper Changes

```ts
// lib/utils.ts
export const isUserPro = (user: User | null) =>
  !!user &&
  user.subscription_tier === SubscriptionTier.PRO &&
  new Date(user.subscription_end_date ?? 0) > new Date()
```

Update `isUserSubscribing()` to call a new helper **`hasPaidFeatures()`** that returns `true` for `PRO`, `GUEST`, `ULTRA`, `BUSINESS`.
This keeps logic DRY when more tiers are added.

### 4.2. UpdateUserRecord Helper

In `app/api/payments/webhook/utils.ts` → `updateUserRecord()`

```ts
subscription_tier: SubscriptionTier.PRO, // NEW
```

Add symmetrical downgrade in the new `handleSubscriptionDeleted()` helper.

---

## 5. Front-End Integration

### 5.1. Surface Tier via **NextAuth** Session (no legacy `getCurrentUser`)

We inject the tier once during JWT callback and expose it to the browser via `session` callback:

```ts
// app/api/auth/[...nextauth]/route.ts
import prisma from '@/app/libs/prismadb'

callbacks: {
  async jwt({ token, user }) {
    // user is available only on sign-in; thereafter rely on token
    if (user) token.uid = user.id

    if (token.uid) {
      const dbUser = await prisma.user.findUnique({ where: { id: token.uid } })
      token.tier = dbUser?.subscription_tier ?? 'FREE'
      token.tierExpiry = dbUser?.subscription_end_date ?? null
      token.cancelPending = dbUser?.subscription_cancel_pending ?? false
    }
    return token
  },
  async session({ session, token }) {
    session.user.subscription = {
      tier: token.tier,
      expiry: token.tierExpiry,
      cancelPending: token.cancelPending,
    }
    return session
  },
}
```

Front-end usage:

```ts
import { useSession } from 'next-auth/react'

const { data } = useSession()
const { tier, expiry, cancelPending } = data?.user.subscription ?? {}
const hasProFeatures = ['PRO', 'GUEST', 'ULTRA', 'BUSINESS'].includes(tier)
```

This removes the need for extra API calls and keeps the tier info refreshed automatically on page reload.

### 5.2. React Hook

Extend existing `useSubscription` (file already exists):

```ts
const { tier, expiry, cancelPending } = useSWR('/user/sub', getUserSubscription)
const isPro = tier === 'PRO' && (!expiry || new Date(expiry) > new Date())
```

Expose `{ tier, isPro, cancelPending }` so any component can:

```tsx
if (!isPro) return <UpgradeCTA />
```

### 5.3. UI Guidelines (multi-tier aware)

Maintain a **permission map** in a single file (`app/configs/tier-permissions.ts`) so adding a tier only requires editing one place:

```ts
export const TierPermissions = {
  FREE: { maxConversations: 5, quickResearch: false },
  PRO: { maxConversations: 42, quickResearch: true },
  GUEST: { maxConversations: 42, quickResearch: true },
  ULTRA: { maxConversations: 200, quickResearch: true },
  BUSINESS: { maxConversations: 500, quickResearch: true },
} as const

export type TierName = keyof typeof TierPermissions
```

Components read from this map instead of hard-coding `isPro` checks.

---

## 6. Edge-Case Handling Matrix

| Case                       | Stripe Emits                                                             | Our Response                                | User Experience              |
| -------------------------- | ------------------------------------------------------------------------ | ------------------------------------------- | ---------------------------- |
| Payment succeeds           | `checkout.session.completed` → `customer.subscription.created`           | Set tier `PRO`, expiry = period_end         | Immediate unlock             |
| User clicks "Cancel"       | `customer.subscription.updated` (`cancel_at_period_end=true`)            | Keep tier `PRO`, set `cancelPending=true`   | Banner: "Pro until mm/dd"    |
| Period ends                | `customer.subscription.deleted`                                          | Downgrade tier `FREE`, clear expiry & flags | Loss of Pro features         |
| Card fails (1st attempt)   | `invoice.payment_failed`                                                 | Optional: email + in-app toast              | Still Pro (per Stripe grace) |
| Card fails (after retries) | `customer.subscription.updated` (`status=unpaid`) → eventually `deleted` | Downgrade to FREE                           | Lose Pro                     |

---

## 7. `GUEST` Tier – **Friends & Testers**

`GUEST` stays **permanently** and grants the **same permissions** as `PRO` but costs $0. You will create these users directly in the DB or via an internal admin panel.

Implementation notes:

1. `hasPaidFeatures()` returns `true` for GUEST.
2. Stripe webhooks **never** modify GUEST records because they have no `subscription_id`.
3. Admin tooling: simple script → `UPDATE "User" SET subscription_tier='GUEST' WHERE email IN (…);`

---

## 11. Implementation Phase – Coding-Agent Playbook

> Follow these steps **in order**. Tick each checkbox before moving to the next.
> All path references are relative to the monorepo root.

### 11.1 Migration & Schema

1. [ ] **`prisma/schema.prisma`** – confirm `SubscriptionTier` enum contains `FREE | PRO | GUEST | ULTRA | BUSINESS` _in that order_ and `subscription_cancel_pending` is present on `User`.
2. [ ] Create a migration: `npx prisma migrate dev -n add_pro_ultra_business_tiers`.
3. [ ] Seed back-fill script (one-off):
   ```sql
   UPDATE "User"
   SET    subscription_tier = 'PRO'
   WHERE  subscription_end_date > NOW()
     AND  subscription_tier = 'FREE';
   ```

### 11.2 Environment & Stripe Setup

1. [ ] `.env` – ensure `STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`, `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` are set for **dev** and **prod**.
2. [ ] Stripe Dashboard:
       • Create/verify _Price_ objects – each must include `metadata.tier = 'PRO' | 'ULTRA' | 'BUSINESS'`.
       • Enable **“Limit customers to one subscription”**.
       • Disable **Cash App Pay**.
3. [ ] Add webhook endpoint `<domain>/api/payments/webhook` with allowed events list from §10.1.

### 11.3 Auth & Session

1. [ ] Implement NextAuth callbacks in `app/api/auth/[...nextauth]/route.ts` exactly as shown in §5.1.
2. [ ] Add `subscription` key to the `Session` type declaration (`next-auth.d.ts`).

### 11.4 Stripe Sync Logic

1. [ ] Create `lib/stripe/sync.ts` exporting `syncStripeState(customerId: string)` which:
       a. Lists subscriptions (`status: 'all'`).
       b. Writes/updates row in `Subscription`.
       c. Optionally records invoices to new `Invoice` table (future).
2. [ ] Update `app/api/payments/webhook/utils.ts` to call `syncStripeState` in `handleWebhookEvent`.
3. [ ] Implement `/subscription/success` route that pulls `customerId` from session → calls `syncStripeState` then redirects.

### 11.5 Front-End Gating

1. [ ] Add `app/configs/tier-permissions.ts` map (§5.3).
2. [ ] Refactor all `isSubscribed` checks to `hasProFeatures` or permission lookup.
3. [ ] Update Pricing UI to hide Upgrade button when `tier ∈ {PRO, GUEST, ULTRA, BUSINESS}` and `!cancelPending`.

### 11.6 Analytics (PostHog)

1. [ ] Add `posthog-js` client lib.
2. [ ] Send `$identify` with `distinct_id = user.id` and `tier` property.
3. [ ] Track `tier_change` event inside `syncStripeState` when the tier differs from previous snapshot.

### 11.7 Cron / Reconciliation

1. [ ] Add `scripts/reconcileStripe.ts` – lists local customers not updated in 24 h → calls `syncStripeState`.
2. [ ] Schedule with `vercel cron` or GitHub Actions nightly.

### 11.8 Regression Test Matrix

| Scenario              | Expected tier | cancelPending | Access allowed?     |
| --------------------- | ------------- | ------------- | ------------------- |
| New checkout succeeds | PRO           | false         | ✅                  |
| User clicks Cancel    | PRO           | true          | ✅ until period end |
| Period ends           | FREE          | false         | ❌ premium          |
| GUEST manual promote  | GUEST         | false         | ✅                  |
| ULTRA price plan      | ULTRA         | false         | ✅ + higher limits  |

### 11.9 Done-Criteria

- All unit & integration tests pass.
- Manual checkout flow verified with Stripe test card `4242 4242 4242 4242`.
- `syncStripeState` snapshot visible in DB for each event.
- PostHog shows `tier_change` events.
- Cron reconciliation logs "0 stale customers" after 24 h.

---

## 12. Appendix – Key Code Diffs (pseudo-patches)

_(Moved down from earlier section for readability; unchanged content follows.)_
