'use client'

import React from 'react'
import {
  AssistantRuntimeProvider,
  ThreadPrimitive,
  ComposerPrimitive,
} from '@assistant-ui/react'
import { useChatRuntime } from '@assistant-ui/react-ai-sdk'

const demoMessages = [
  {
    id: '1',
    role: 'user' as const,
    content: 'Hello! This is a test of the assistant-ui library.',
    createdAt: new Date(),
  },
  {
    id: '2',
    role: 'assistant' as const,
    content: 'Hello! I am rendering correctly with static data. The library is working.',
    createdAt: new Date(),
  },
  {
    id: '3',
    role: 'user' as const,
    content: 'Great! This confirms the issue is not with the library itself.',
    createdAt: new Date(),
  },
]

export default function AssistantUIDemoPage() {
  const runtime = useChatRuntime({
    initialMessages: demoMessages,
    // No API endpoint needed for this static demo
  })

  return (
    <div className="h-screen w-screen bg-white flex flex-col">
      <div className="p-4 border-b bg-gray-50">
        <h1 className="text-xl font-semibold">Assistant-UI Static Demo</h1>
        <p className="text-sm text-gray-600">
          This page demonstrates that the `assistant-ui` components can render
          correctly with hardcoded data.
        </p>
      </div>
      <AssistantRuntimeProvider runtime={runtime}>
        <div className="flex-1 min-h-0">
          <ThreadPrimitive.Root>
            <ThreadPrimitive.Messages />
          </ThreadPrimitive.Root>
        </div>
        <div className="p-4 border-t">
          <ComposerPrimitive.Root>
            <ComposerPrimitive.Input />
            <ComposerPrimitive.Send />
          </ComposerPrimitive.Root>
        </div>
      </AssistantRuntimeProvider>
    </div>
  )
}
