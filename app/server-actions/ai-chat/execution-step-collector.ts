import type { AiStepType } from '@prisma/client'
import type { ExecutionStepData } from './types'
import {
  type ThoughtStepMetadata,
  type ToolCallStepMetadata,
  type ToolResultStepMetadata,
  type ReasoningSummaryStepMetadata,
  createEnhancedToolResult,
} from '@/app/types/ai-metadata'

/**
 * ExecutionStepCollector is responsible for collecting execution steps during AI processing
 * It maintains the step order and handles various step types according to the design spec
 */
export class ExecutionStepCollector {
  private steps: ExecutionStepData[] = []
  private currentStepOrder: number = 0
  private parallelGroups: Map<string, number> = new Map()

  /**
   * Adds a THOUGHT step - internal AI reasoning
   */
  addThought(
    reasoning: string,
    options: {
      thinkingTime?: number
      complexity?: 'simple' | 'moderate' | 'complex'
      category?: 'analysis' | 'planning' | 'evaluation' | 'synthesis'
      confidence?: number
      duration?: number
    } = {}
  ): void {
    const thoughtMetadata: ThoughtStepMetadata = {
      reasoning,
      timestamp: Date.now(),
      thinkingTime: options.thinkingTime,
      complexity: options.complexity,
      category: options.category,
      confidence: options.confidence,
      duration: options.duration,
    }
    this.addStep('THOUGHT', thoughtMetadata)
  }

  /**
   * Adds a TOOL_CALL step - when AI calls a tool/function
   */
  addToolCall(
    toolName: string,
    args: Record<string, any>,
    options: {
      expectedResultType?: string
      priority?: 'low' | 'medium' | 'high'
      retryCount?: number
      confidence?: number
      duration?: number
    } = {}
  ): void {
    const toolCallMetadata: ToolCallStepMetadata = {
      toolName,
      args,
      timestamp: Date.now(),
      expectedResultType: options.expectedResultType,
      priority: options.priority,
      retryCount: options.retryCount,
      confidence: options.confidence,
      duration: options.duration,
    }
    this.addStep('TOOL_CALL', toolCallMetadata)
  }

  /**
   * Adds a TOOL_RESULT step - result from a tool call
   */
  addToolResult(
    toolName: string,
    result: any,
    options: {
      error?: string
      resultSize?: number
      resultType?: string
      contextProvided?: {
        summary?: string
        keyPoints?: string[]
        relevanceScore?: number
        sourceCount?: number
      }
      confidence?: number
      duration?: number
    } = {}
  ): void {
    const toolResultMetadata: ToolResultStepMetadata = {
      toolName,
      result,
      timestamp: Date.now(),
      success: !options.error,
      error: options.error,
      resultSize: options.resultSize || JSON.stringify(result).length,
      resultType: options.resultType || typeof result,
      contextProvided: options.contextProvided,
      confidence: options.confidence,
      duration: options.duration,
    }
    this.addStep('TOOL_RESULT', toolResultMetadata)
  }

  /**
   * Adds a REASONING_SUMMARY step - high-level summary of AI's reasoning
   */
  addReasoningSummary(
    summary: string,
    options: {
      keyDecisions?: string[]
      alternativesConsidered?: string[]
      confidenceLevel?: number
      reasoningChain?: string[]
      duration?: number
    } = {}
  ): void {
    const reasoningSummaryMetadata: ReasoningSummaryStepMetadata = {
      summary,
      timestamp: Date.now(),
      keyDecisions: options.keyDecisions,
      alternativesConsidered: options.alternativesConsidered,
      confidenceLevel: options.confidenceLevel,
      reasoningChain: options.reasoningChain,
      duration: options.duration,
    }
    this.addStep('REASONING_SUMMARY', reasoningSummaryMetadata)
  }

  /**
   * Adds a SUB_AGENT_INVOCATION step - when AI invokes a sub-agent
   */
  addSubAgentInvocation(
    agentName: string,
    task: string,
    metadata: Record<string, any> = {}
  ): void {
    this.addStep('SUB_AGENT_INVOCATION', {
      agentName,
      task,
      timestamp: Date.now(),
      ...metadata,
    })
  }

  /**
   * Adds a parallel execution step with a shared parallelKey
   */
  addParallelStep(
    parallelKey: string,
    stepType: AiStepType,
    metadata: Record<string, any>,
    parentStepId?: string
  ): void {
    const step: ExecutionStepData = {
      stepOrder: this.currentStepOrder++,
      type: stepType,
      metadata: {
        ...metadata,
        timestamp: Date.now(),
      },
      parallelKey,
      parentStepId,
    }

    this.steps.push(step)
  }

  /**
   * Adds a generic execution step
   */
  private addStep(
    type: AiStepType,
    metadata: Record<string, any>,
    parallelKey?: string,
    parentStepId?: string
  ): void {
    const step: ExecutionStepData = {
      stepOrder: this.currentStepOrder++,
      type,
      metadata,
      parallelKey,
      parentStepId,
    }

    this.steps.push(step)
  }

  /**
   * Parses legacy <thinking> tags from AI responses and converts them to steps
   * This maintains backward compatibility with existing implementations
   * @deprecated Use proper ExecutionStep collection instead of thinking tags
   */
  parseThinkingTags(content: string): {
    cleanContent: string
    steps: ExecutionStepData[]
  } {
    // Legacy thinking tag parsing is disabled by default for production
    if (process.env.ENABLE_LEGACY_THINKING_TAGS !== 'true') {
      return { cleanContent: content, steps: [] }
    }
    const thinkingRegex = /<thinking(?:\s+time="([^"]*)")?>([^]*?)<\/thinking>/g
    const steps: ExecutionStepData[] = []
    let match: RegExpExecArray | null
    let currentOrder = this.currentStepOrder

    while ((match = thinkingRegex.exec(content)) !== null) {
      const thinkingTime = match[1] ? parseInt(match[1]) : null
      const thinking = match[2].trim()

      // Parse different types of content within thinking tags
      if (
        thinking.includes('🌐') ||
        thinking.includes('📊') ||
        thinking.includes('🧮') ||
        thinking.includes('📖') ||
        thinking.includes('🔍')
      ) {
        // Contains tool calls - parse them
        const toolSections = thinking.split(/\n\n(?=[🌐📊🧮📖🔍])/g)

        for (const section of toolSections) {
          if (/^[🌐📊🧮📖🔍]/.test(section)) {
            // Tool call section
            const toolMatch = section.match(/^([🌐📊🧮📖🔍])\s*([^\n]+)/)
            if (toolMatch) {
              const toolName = toolMatch[2]
              const toolContent = section.replace(
                /^[🌐📊🧮📖🔍]\s*[^\n]+\n?/,
                ''
              )

              steps.push({
                stepOrder: currentOrder++,
                type: 'TOOL_CALL',
                metadata: {
                  toolName,
                  args: { query: toolContent },
                  icon: toolMatch[1],
                  timestamp: Date.now(),
                },
              })
            }
          } else {
            // Regular thinking
            steps.push({
              stepOrder: currentOrder++,
              type: 'THOUGHT',
              metadata: {
                reasoning: section,
                timestamp: Date.now(),
              },
            })
          }
        }
      } else {
        // Regular thinking content
        steps.push({
          stepOrder: currentOrder++,
          type: 'THOUGHT',
          metadata: {
            reasoning: thinking,
            thinkingTime,
            timestamp: Date.now(),
          },
        })
      }
    }

    // Remove thinking tags from content
    const cleanContent = content.replace(thinkingRegex, '').trim()

    // Add final reasoning summary if we extracted steps
    if (steps.length > 0) {
      steps.push({
        stepOrder: currentOrder++,
        type: 'REASONING_SUMMARY',
        metadata: {
          summary: `Processed ${steps.length} reasoning steps and generated response`,
          timestamp: Date.now(),
        },
      })
    }

    return { cleanContent, steps }
  }

  /**
   * Returns all collected steps
   */
  getSteps(): ExecutionStepData[] {
    return [...this.steps]
  }

  /**
   * Returns the number of steps collected
   */
  getStepCount(): number {
    return this.steps.length
  }

  /**
   * Clears all collected steps
   */
  clear(): void {
    this.steps = []
    this.currentStepOrder = 0
    this.parallelGroups.clear()
  }

  /**
   * Gets steps by type
   */
  getStepsByType(type: AiStepType): ExecutionStepData[] {
    return this.steps.filter(step => step.type === type)
  }

  /**
   * Gets steps by parallel key
   */
  getStepsByParallelKey(parallelKey: string): ExecutionStepData[] {
    return this.steps.filter(step => step.parallelKey === parallelKey)
  }

  /**
   * Creates a summary of all execution steps for logging/debugging
   */
  getSummary(): {
    totalSteps: number
    stepsByType: Record<AiStepType, number>
    parallelGroups: number
    hasErrors: boolean
  } {
    const stepsByType: Record<AiStepType, number> = {
      THOUGHT: 0,
      TOOL_CALL: 0,
      TOOL_RESULT: 0,
      REASONING_SUMMARY: 0,
      SUB_AGENT_INVOCATION: 0,
    }

    let hasErrors = false
    const parallelKeys = new Set<string>()

    for (const step of this.steps) {
      stepsByType[step.type]++
      if (step.parallelKey) {
        parallelKeys.add(step.parallelKey)
      }
      if (step.metadata.error) {
        hasErrors = true
      }
    }

    return {
      totalSteps: this.steps.length,
      stepsByType,
      parallelGroups: parallelKeys.size,
      hasErrors,
    }
  }
}

/**
 * Utility function to create a new ExecutionStepCollector instance
 */
export function createExecutionStepCollector(): ExecutionStepCollector {
  return new ExecutionStepCollector()
}

/**
 * Utility function to parse thinking tags from legacy content
 * @deprecated Use proper ExecutionStep collection instead of thinking tags
 */
export function parseThinkingContent(content: string): {
  cleanContent: string
  steps: ExecutionStepData[]
} {
  const collector = createExecutionStepCollector()
  return collector.parseThinkingTags(content)
}
