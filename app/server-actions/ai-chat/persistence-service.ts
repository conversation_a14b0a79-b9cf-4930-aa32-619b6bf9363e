import prisma from '@/app/libs/prismadb'
import {
  generateAiConversationId,
  generateAiMessageId,
  generateAiExecutionStepId,
} from '@/lib/id'
import {
  validateMessageRole,
  validateConversationId,
  validateStepOrder,
  validateStepType,
  validateUserId,
} from '@/app/libs/ai-chat/validation'
import { persistenceLogger } from './logging'
import { normalizeStepOrders, buildAttachmentCreate } from './utils'
import type {
  ConversationCreateData,
  ConversationTurnData,
  ConversationWithMessages,
  PersistenceResult,
  MessageData,
} from './types'

/**
 * Creates a new AI conversation with human-readable ID
 */
export async function createAiConversation(
  data: ConversationCreateData,
  conversationId?: string
): Promise<PersistenceResult<{ conversationId: string }>> {
  try {
    // Validate input data
    validateUserId(data.userId)

    const finalConversationId = conversationId || generateAiConversationId()

    // Validate provided conversationId if given
    if (conversationId) {
      validateConversationId(conversationId)
    }

    const conversation = await prisma.aiConversation.create({
      data: {
        id: finalConversationId,
        userId: data.userId,
        title: data.title,
        contextEntityType: data.contextEntityType,
        contextEntityId: data.contextEntityId,
      },
    })

    return {
      success: true,
      data: { conversationId: conversation.id },
    }
  } catch (error) {
    persistenceLogger.error('Failed to create AI conversation', { error })
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to create conversation',
    }
  }
}

/**
 * Atomically persists a complete conversation turn (user message + assistant response)
 * This is the core function that ensures data integrity during streaming
 */
export async function persistConversationTurn(
  data: ConversationTurnData
): Promise<
  PersistenceResult<{ userMessageId: string; assistantMessageId: string }>
> {
  try {
    // Validate input data
    validateConversationId(data.conversationId)

    // Generate IDs for the messages
    const userMessageId = generateAiMessageId()
    const assistantMessageId = generateAiMessageId()

    // Use transaction to ensure atomicity
    const result = await prisma.$transaction(async tx => {
      // 1. Verify conversation exists (throws if not found)
      await tx.aiConversation.findUniqueOrThrow({
        where: { id: data.conversationId },
        select: { id: true }, // Only select id for efficiency
      })

      // 2. Create user message
      const userMessage = await tx.aiMessage.create({
        data: {
          id: userMessageId,
          conversationId: data.conversationId,
          role: validateMessageRole(data.userMessage.role),
          content: data.userMessage.content,
          // User messages typically don't have steps or attachments
          // but we can support them if needed
          ...(data.userMessage.attachments?.length && {
            attachments: {
              create: data.userMessage.attachments.map(buildAttachmentCreate),
            },
          }),
        },
      })

      // 3. Create assistant message with execution steps
      const assistantMessage = await tx.aiMessage.create({
        data: {
          id: assistantMessageId,
          conversationId: data.conversationId,
          role: validateMessageRole(data.assistantMessage.role),
          content: data.assistantMessage.content,
          // Create execution steps if provided
          ...(data.assistantMessage.steps?.length && {
            steps: {
              create: normalizeStepOrders(
                data.assistantMessage.steps.map(step => {
                  // Validate before normalization to avoid double-work
                  validateStepOrder(step.stepOrder)
                  validateStepType(step.type)
                  return step
                })
              ).map(step => ({
                id: generateAiExecutionStepId(),
                stepOrder: step.stepOrder,
                type: step.type,
                metadata: step.metadata,
                parallelKey: step.parallelKey,
                parentStepId: step.parentStepId,
              })),
            },
          }),
          // Create attachments if provided
          ...(data.assistantMessage.attachments?.length && {
            attachments: {
              create: data.assistantMessage.attachments.map(
                buildAttachmentCreate
              ),
            },
          }),
        },
      })

      // 4. Conversation timestamp is updated automatically by @updatedAt
      // No need to manually update it

      return {
        userMessageId: userMessage.id,
        assistantMessageId: assistantMessage.id,
      }
    })

    return {
      success: true,
      data: result,
    }
  } catch (error) {
    persistenceLogger.error('Failed to persist conversation turn', { error })
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to persist conversation turn',
    }
  }
}

/**
 * Adds a single message to an existing conversation
 * Useful for system messages or single-turn interactions
 */
export async function addMessageToConversation(
  conversationId: string,
  messageData: MessageData
): Promise<PersistenceResult<{ messageId: string }>> {
  try {
    // Validate input data
    validateConversationId(conversationId)

    const messageId = generateAiMessageId()

    const message = await prisma.aiMessage.create({
      data: {
        id: messageId,
        conversationId,
        role: validateMessageRole(messageData.role),
        content: messageData.content,
        ...(messageData.steps?.length && {
          steps: {
            create: normalizeStepOrders(
              messageData.steps.map(step => {
                // Validate before normalization to avoid double-work
                validateStepOrder(step.stepOrder)
                validateStepType(step.type)
                return step
              })
            ).map(step => ({
              id: generateAiExecutionStepId(),
              stepOrder: step.stepOrder,
              type: step.type,
              metadata: step.metadata,
              parallelKey: step.parallelKey,
              parentStepId: step.parentStepId,
            })),
          },
        }),
        ...(messageData.attachments?.length && {
          attachments: {
            create: messageData.attachments.map(buildAttachmentCreate),
          },
        }),
      },
    })

    return {
      success: true,
      data: { messageId: message.id },
    }
  } catch (error) {
    persistenceLogger.error('Failed to add message to conversation', { error })
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to add message to conversation',
    }
  }
}

/**
 * Retrieves a conversation with all its messages and execution steps
 */
export async function getConversationWithMessages(
  conversationId: string
): Promise<PersistenceResult<ConversationWithMessages>> {
  try {
    // Validate input data
    validateConversationId(conversationId)

    const conversation = await prisma.aiConversation.findUnique({
      where: { id: conversationId },
      include: {
        messages: {
          include: {
            steps: {
              orderBy: { stepOrder: 'asc' },
            },
            attachments: true,
          },
          orderBy: { createdAt: 'asc' },
        },
      },
    })

    if (!conversation) {
      return {
        success: false,
        error: 'Conversation not found',
      }
    }

    return {
      success: true,
      data: conversation as ConversationWithMessages,
    }
  } catch (error) {
    persistenceLogger.error('Failed to get conversation with messages', {
      error,
    })
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to retrieve conversation',
    }
  }
}

/**
 * Optimized conversation retrieval with pagination support
 * Only loads essential message data, excludes execution steps by default
 */
export async function getConversationWithMessagesPaginated(
  conversationId: string,
  options: {
    limit?: number
    cursor?: string // Message ID to start pagination from
    includeSteps?: boolean
    includeAttachments?: boolean
  } = {}
): Promise<
  PersistenceResult<{
    conversation: {
      id: string
      title: string | null
      contextEntityType: string | null
      contextEntityId: string | null
      createdAt: Date
      userId: string
    }
    messages: Array<{
      id: string
      role: string
      content: string
      createdAt: Date
      stepCount?: number
      steps?: any[]
      attachments?: any[]
    }>
    pagination: {
      hasMore: boolean
      nextCursor?: string
      limit: number
      total: number
    }
  }>
> {
  try {
    // Validate input data
    validateConversationId(conversationId)

    const {
      limit = 50,
      cursor,
      includeSteps = false,
      includeAttachments = true,
    } = options

    // First, get conversation metadata
    const conversation = await prisma.aiConversation.findUnique({
      where: { id: conversationId },
      select: {
        id: true,
        title: true,
        contextEntityType: true,
        contextEntityId: true,
        createdAt: true,
        userId: true,
      },
    })

    if (!conversation) {
      return {
        success: false,
        error: 'Conversation not found',
      }
    }

    // Get total message count for pagination info
    const totalMessages = await prisma.aiMessage.count({
      where: { conversationId },
    })

    // Build cursor condition for pagination (load OLDER messages than cursor)
    let cursorCondition = {}
    if (cursor) {
      // Extract creation time of the cursor message so we can fetch older items
      const cursorMessage = await prisma.aiMessage.findUnique({
        where: { id: cursor },
        select: { createdAt: true },
      })

      if (cursorMessage) {
        cursorCondition = {
          createdAt: { lt: cursorMessage.createdAt },
        }
      }
    }

    // Optimized query - only select necessary fields
    const rows = await prisma.aiMessage.findMany({
      where: {
        conversationId,
        ...cursorCondition,
      },
      select: {
        id: true,
        role: true,
        content: true,
        createdAt: true,
        // Conditional includes based on options
        ...(includeSteps && {
          steps: {
            select: {
              id: true,
              stepOrder: true,
              type: true,
              metadata: true,
              parallelKey: true,
              parentStepId: true,
              createdAt: true,
              updatedAt: true,
            },
            orderBy: { stepOrder: 'asc' },
          },
        }),
        ...(includeAttachments && {
          attachments: {
            select: {
              id: true,
              fileName: true,
              fileType: true,
              fileSize: true,
              url: true,
              createdAt: true,
            },
          },
        }),
        // Always include step count for UI purposes
        _count: {
          select: {
            steps: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' }, // newest first
      take: limit + 1, // Take one extra to know if more pages exist
    })

    // Determine pagination & slice
    const hasMore = rows.length > limit
    const slice = hasMore ? rows.slice(0, limit) : rows
    const messagesDesc = slice // still newest→oldest
    const nextCursor = hasMore
      ? messagesDesc[messagesDesc.length - 1]?.id
      : undefined

    // We return oldest→newest to UI for natural reading order
    const messagesAsc = messagesDesc.reverse()

    // Transform messages to include stepCount
    const transformedMessages = messagesAsc.map(message => {
      const transformed = {
        id: message.id,
        role: message.role,
        content: message.content,
        createdAt: message.createdAt,
        stepCount: message._count.steps,
        ...(includeSteps && message.steps && { steps: message.steps }),
        ...(includeAttachments &&
          message.attachments && { attachments: message.attachments }),
      }

      // Debug logging for messages with steps
      if (message._count.steps > 0) {
        console.log(`🔍 [PersistenceService] Message ${message.id}:`, {
          stepCount: message._count.steps,
          includeSteps,
          hasStepsArray: !!message.steps,
          stepsLength: message.steps?.length,
          webSearchSteps: message.steps?.filter(
            s => s.metadata?.toolName === 'web_search'
          ).length,
        })
      }

      return transformed
    })

    return {
      success: true,
      data: {
        conversation,
        messages: transformedMessages,
        pagination: {
          hasMore,
          nextCursor,
          limit,
          total: totalMessages,
        },
      },
    }
  } catch (error) {
    persistenceLogger.error(
      'Failed to get conversation with messages (paginated)',
      { error }
    )
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to retrieve conversation',
    }
  }
}

/**
 * Lists conversations for a user with basic info
 */
export async function getUserConversations(
  userId: string,
  limit: number = 50
): Promise<
  PersistenceResult<
    Array<{
      id: string
      title: string | null
      createdAt: Date
      updatedAt: Date
    }>
  >
> {
  try {
    // Validate input data
    validateUserId(userId)

    const conversations = await prisma.aiConversation.findMany({
      where: { userId },
      select: {
        id: true,
        title: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { updatedAt: 'desc' },
      take: limit,
    })

    return {
      success: true,
      data: conversations,
    }
  } catch (error) {
    persistenceLogger.error('Failed to get user conversations', { error })
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to retrieve conversations',
    }
  }
}

/**
 * Deletes a conversation and all its associated data
 */
export async function deleteConversation(
  conversationId: string
): Promise<PersistenceResult<{ deleted: boolean }>> {
  try {
    // Validate input data
    validateConversationId(conversationId)

    await prisma.aiConversation.delete({
      where: { id: conversationId },
    })

    return {
      success: true,
      data: { deleted: true },
    }
  } catch (error) {
    persistenceLogger.error('Failed to delete conversation', { error })
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to delete conversation',
    }
  }
}

/**
 * Updates conversation title
 */
export async function updateConversationTitle(
  conversationId: string,
  title: string
): Promise<PersistenceResult<{ updated: boolean }>> {
  try {
    // Validate input data
    validateConversationId(conversationId)

    await prisma.aiConversation.update({
      where: { id: conversationId },
      data: { title },
    })

    return {
      success: true,
      data: { updated: true },
    }
  } catch (error) {
    persistenceLogger.error('Failed to update conversation title', { error })
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to update conversation title',
    }
  }
}

/**
 * Gets conversations by context entity (polymorphic support)
 */
export async function getConversationsByContext(
  contextEntityType: string,
  contextEntityId: string
): Promise<
  PersistenceResult<
    Array<{
      id: string
      title: string | null
      createdAt: Date
      updatedAt: Date
    }>
  >
> {
  try {
    // Basic validation for context parameters
    if (!contextEntityType || !contextEntityId) {
      throw new Error('contextEntityType and contextEntityId are required')
    }

    const conversations = await prisma.aiConversation.findMany({
      where: {
        contextEntityType,
        contextEntityId,
      },
      select: {
        id: true,
        title: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { updatedAt: 'desc' },
    })

    return {
      success: true,
      data: conversations,
    }
  } catch (error) {
    persistenceLogger.error('Failed to get conversations by context', { error })
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Failed to retrieve conversations by context',
    }
  }
}
