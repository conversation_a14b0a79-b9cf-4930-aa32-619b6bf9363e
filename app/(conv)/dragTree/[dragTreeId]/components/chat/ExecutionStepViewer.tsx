/* eslint-disable react/display-name */
'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  FiChevronDown,
  FiChevronRight,
  FiCpu,
  FiSearch,
  FiTool,
  FiFileText,
  FiUsers,
  FiClock,
  FiExternalLink,
  FiCopy,
} from 'react-icons/fi'
import { AiStepType } from '@prisma/client'
import toast from 'react-hot-toast'

type ExecutionStep = {
  id: string
  stepOrder: number
  type: AiStepType
  metadata: any
  parallelKey?: string | null
  parentStepId?: string | null
  createdAt: string
  updatedAt: string
}

type ExecutionStepViewerProps = {
  steps: ExecutionStep[]
  isLoading?: boolean
  className?: string
}

type StepDisplayProps = {
  step: ExecutionStep
  isExpanded: boolean
  onToggle: () => void
}

// Helper function to get step type icon and color
function getStepTypeInfo(type: AiStepType) {
  switch (type) {
    case AiStepType.THOUGHT:
      return {
        icon: Fi<PERSON><PERSON>,
        label: 'Thinking',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
      }
    case AiStepType.TOOL_CALL:
      return {
        icon: FiTool,
        label: 'Tool Call',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
      }
    case AiStepType.TOOL_RESULT:
      return {
        icon: FiFileText,
        label: 'Tool Result',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
      }
    case AiStepType.REASONING_SUMMARY:
      return {
        icon: FiFileText,
        label: 'Summary',
        color: 'text-purple-600',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200',
      }
    case AiStepType.SUB_AGENT_INVOCATION:
      return {
        icon: FiUsers,
        label: 'Sub-Agent',
        color: 'text-indigo-600',
        bgColor: 'bg-indigo-50',
        borderColor: 'border-indigo-200',
      }
    default:
      return {
        icon: FiCpu,
        label: 'Unknown',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
      }
  }
}

// Helper function to format time difference
function getTimeAgo(timestamp: string): string {
  const now = new Date()
  const time = new Date(timestamp)
  const diffMs = now.getTime() - time.getTime()
  const diffSecs = Math.floor(diffMs / 1000)

  if (diffSecs < 60) return `${diffSecs}s ago`
  const diffMins = Math.floor(diffSecs / 60)
  if (diffMins < 60) return `${diffMins}m ago`
  const diffHours = Math.floor(diffMins / 60)
  if (diffHours < 24) return `${diffHours}h ago`
  const diffDays = Math.floor(diffHours / 24)
  return `${diffDays}d ago`
}

// Copy to clipboard helper with fallback
function copyToClipboard(text: string, label: string) {
  if (!navigator.clipboard) {
    // Fallback for older browsers or insecure contexts
    try {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      toast.success(`${label} copied to clipboard`)
    } catch {
      toast.error('Copy not supported in this browser')
    }
    return
  }

  navigator.clipboard
    .writeText(text)
    .then(() => {
      toast.success(`${label} copied to clipboard`)
    })
    .catch(() => {
      toast.error('Failed to copy to clipboard')
    })
}

// Individual step display component
const StepDisplay: React.FC<StepDisplayProps> = React.memo(
  ({ step, isExpanded, onToggle }) => {
    const typeInfo = getStepTypeInfo(step.type)
    const Icon = typeInfo.icon
    const timeAgo = getTimeAgo(step.createdAt)

    // Parse metadata based on step type
    const renderStepContent = () => {
      const metadata = step.metadata || {}

      switch (step.type) {
        case AiStepType.THOUGHT:
          return (
            <div className="space-y-2">
              {metadata.reasoning && (
                <div className="text-sm text-gray-700 italic">
                  "{metadata.reasoning}"
                </div>
              )}
              {metadata.timestamp && (
                <div className="text-xs text-gray-500">
                  <FiClock className="inline w-3 h-3 mr-1" />
                  {new Date(metadata.timestamp).toLocaleTimeString()}
                </div>
              )}
            </div>
          )

        case AiStepType.TOOL_CALL:
          const toolName = metadata.toolName || 'unknown'
          const args = metadata.args || {}

          return (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                {toolName === 'web_search' && <FiSearch className="w-4 h-4" />}
                <span className="font-medium">{toolName}</span>
              </div>
              {Object.keys(args).length > 0 && (
                <div className="text-sm space-y-1">
                  {Object.entries(args).map(([key, value]) => (
                    <div key={key} className="flex items-start gap-2">
                      <span className="text-gray-500 min-w-0 shrink-0">
                        {key}:
                      </span>
                      <span className="text-gray-700 break-words">
                        {String(value)}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )

        case AiStepType.TOOL_RESULT:
          const result = metadata.result
          const error = metadata.error

          return (
            <div className="space-y-2">
              {error ? (
                <div className="text-sm text-red-600 font-medium">
                  Error: {error}
                </div>
              ) : result ? (
                <div className="space-y-2">
                  {typeof result === 'object' ? (
                    <div className="text-sm space-y-1">
                      {Object.entries(result).map(([key, value]) => (
                        <div key={key} className="flex items-start gap-2">
                          <span className="text-gray-500 min-w-0 shrink-0">
                            {key}:
                          </span>
                          <span className="text-gray-700 break-words">
                            {String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-700 max-h-32 overflow-y-auto">
                      {String(result)}
                    </div>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      copyToClipboard(
                        typeof result === 'object'
                          ? JSON.stringify(result, null, 2)
                          : String(result),
                        'Tool result'
                      )
                    }
                    className="h-6 text-xs"
                  >
                    <FiCopy className="w-3 h-3 mr-1" />
                    Copy
                  </Button>
                </div>
              ) : (
                <div className="text-sm text-gray-500 italic">
                  No result data
                </div>
              )}
            </div>
          )

        case AiStepType.REASONING_SUMMARY:
          return (
            <div className="space-y-2">
              {metadata.summary && (
                <div className="text-sm text-gray-700">{metadata.summary}</div>
              )}
            </div>
          )

        default:
          return (
            <div className="text-sm text-gray-600">
              <pre className="whitespace-pre-wrap overflow-x-auto">
                {JSON.stringify(metadata, null, 2)}
              </pre>
            </div>
          )
      }
    }

    return (
      <div
        className={cn(
          'border rounded-lg transition-all duration-200',
          typeInfo.borderColor,
          typeInfo.bgColor
        )}
      >
        <button
          onClick={onToggle}
          className="w-full p-3 flex items-center gap-3 text-left hover:bg-opacity-80 transition-colors"
        >
          <div className="flex items-center gap-2 flex-1">
            <Icon className={cn('w-4 h-4', typeInfo.color)} />
            <span className={cn('font-medium text-sm', typeInfo.color)}>
              {typeInfo.label}
            </span>
            <span className="text-xs text-gray-500">#{step.stepOrder}</span>
            {step.parallelKey && (
              <span className="text-xs px-1.5 py-0.5 bg-gray-200 rounded text-gray-600">
                parallel: {step.parallelKey}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-500">{timeAgo}</span>
            {isExpanded ? (
              <FiChevronDown className="w-4 h-4 text-gray-500" />
            ) : (
              <FiChevronRight className="w-4 h-4 text-gray-500" />
            )}
          </div>
        </button>

        {isExpanded && (
          <div className="px-3 pb-3 border-t border-gray-200">
            <div className="pt-3">{renderStepContent()}</div>
          </div>
        )}
      </div>
    )
  },
  (prevProps, nextProps) => {
    // Custom comparison for StepDisplay memo
    return (
      prevProps.step.id === nextProps.step.id &&
      prevProps.step.updatedAt === nextProps.step.updatedAt &&
      prevProps.isExpanded === nextProps.isExpanded
    )
  }
)

// Main ExecutionStepViewer component
export const ExecutionStepViewer: React.FC<ExecutionStepViewerProps> =
  React.memo(
    ({ steps, isLoading = false, className }) => {
      const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set())
      const [showAll, setShowAll] = useState<boolean>(false)

      const toggleStep = (stepId: string) => {
        const newExpanded = new Set(expandedSteps)
        if (newExpanded.has(stepId)) {
          newExpanded.delete(stepId)
        } else {
          newExpanded.add(stepId)
        }
        setExpandedSteps(newExpanded)
      }

      const toggleAllSteps = () => {
        if (expandedSteps.size === steps.length) {
          setExpandedSteps(new Set())
        } else {
          setExpandedSteps(new Set(steps.map(step => step.id)))
        }
      }

      if (isLoading) {
        return (
          <div className={cn('space-y-2', className)}>
            <div className="animate-pulse">
              <div className="h-12 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        )
      }

      if (steps.length === 0) {
        return (
          <div
            className={cn(
              'text-sm text-gray-500 italic text-center py-4',
              className
            )}
          >
            No execution steps available
          </div>
        )
      }

      // Sort steps by order
      const sortedSteps = [...steps].sort((a, b) => a.stepOrder - b.stepOrder)

      // Show only first 3 steps initially unless showAll is true
      const displaySteps = showAll ? sortedSteps : sortedSteps.slice(0, 3)
      const hasMore = sortedSteps.length > 3

      return (
        <div className={cn('space-y-3', className)}>
          {/* Header with controls */}
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium text-gray-700">
              Execution Steps ({steps.length})
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleAllSteps}
                className="h-7 text-xs"
              >
                {expandedSteps.size === steps.length
                  ? 'Collapse All'
                  : 'Expand All'}
              </Button>
            </div>
          </div>

          {/* Steps list */}
          <div className="space-y-2">
            {displaySteps.map(step => (
              <StepDisplay
                key={step.id}
                step={step}
                isExpanded={expandedSteps.has(step.id)}
                onToggle={() => toggleStep(step.id)}
              />
            ))}
          </div>

          {/* Show more button */}
          {hasMore && !showAll && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAll(true)}
              className="w-full h-8 text-xs text-gray-600"
            >
              Show {sortedSteps.length - 3} more steps
            </Button>
          )}

          {hasMore && showAll && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAll(false)}
              className="w-full h-8 text-xs text-gray-600"
            >
              Show less
            </Button>
          )}
        </div>
      )
    },
    (prevProps, nextProps) => {
      // Custom comparison for ExecutionStepViewer memo
      if (prevProps.isLoading !== nextProps.isLoading) return false
      if (prevProps.className !== nextProps.className) return false
      if (prevProps.steps.length !== nextProps.steps.length) return false

      // Deep comparison of steps (only check essential fields)
      for (let i = 0; i < prevProps.steps.length; i++) {
        const prevStep = prevProps.steps[i]
        const nextStep = nextProps.steps[i]
        if (
          prevStep.id !== nextStep.id ||
          prevStep.updatedAt !== nextStep.updatedAt ||
          prevStep.stepOrder !== nextStep.stepOrder
        ) {
          return false
        }
      }

      return true
    }
  )

export default ExecutionStepViewer
