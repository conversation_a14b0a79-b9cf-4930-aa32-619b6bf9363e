/* eslint-disable react/display-name */
'use client'

import React, { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  FiChevronDown,
  FiChevronRight,
  FiEye,
  FiEyeOff,
  FiActivity,
  FiClock,
} from 'react-icons/fi'
import { AiStepType } from '@prisma/client'
import { ExecutionStepViewer } from './ExecutionStepViewer'

type ExecutionStep = {
  id: string
  stepOrder: number
  type: AiStepType
  metadata: any
  parallelKey?: string | null
  parentStepId?: string | null
  createdAt: string
  updatedAt: string
}

type ReasoningTimelineProps = {
  messageId: string
  stepCount: number
  isStreaming?: boolean
  liveSteps?: ExecutionStep[] // Real-time steps during streaming
  className?: string
}

// Hook to fetch execution steps on demand
function useExecutionSteps(messageId: string) {
  const [steps, setSteps] = useState<ExecutionStep[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [isLoaded, setIsLoaded] = useState<boolean>(false)

  const fetchSteps = async () => {
    if (isLoaded || isLoading) return

    setIsLoading(true)
    setError(null)

    try {
      console.log(
        `🔍 [ReasoningTimeline] Fetching steps for message: ${messageId}`
      )

      const response = await fetch(`/api/aipane/messages/${messageId}/steps`)

      if (!response.ok) {
        throw new Error(`Failed to fetch steps: ${response.status}`)
      }

      const data = await response.json()
      setSteps(data.steps || [])
      setIsLoaded(true)

      console.log(
        `✅ [ReasoningTimeline] Loaded ${data.steps?.length || 0} steps`
      )
    } catch (err) {
      console.error('Error fetching execution steps:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }

  return {
    steps,
    isLoading,
    error,
    isLoaded,
    fetchSteps,
  }
}

export const ReasoningTimeline: React.FC<ReasoningTimelineProps> = React.memo(
  ({
    messageId,
    stepCount,
    isStreaming = false,
    liveSteps = [],
    className,
  }) => {
    const [isExpanded, setIsExpanded] = useState<boolean>(false)
    const { steps, isLoading, error, isLoaded, fetchSteps } =
      useExecutionSteps(messageId)

    // Combine loaded steps with live streaming steps
    const allSteps = isStreaming ? liveSteps : steps
    const totalStepCount = isStreaming ? liveSteps.length : stepCount



    // Auto-expand during streaming
    useEffect(() => {
      if (isStreaming && liveSteps.length > 0) {
        setIsExpanded(true)
      }
    }, [isStreaming, liveSteps.length])

    const handleToggle = () => {
      if (!isExpanded && !isLoaded && !isStreaming) {
        // First time expanding - fetch steps
        fetchSteps()
      }
      setIsExpanded(!isExpanded)
    }

    // Don't render if no steps
    if (totalStepCount === 0 && !isStreaming) {
      return null
    }

    const renderToggleButton = () => {
      const hasSteps = totalStepCount > 0
      const showSpinner = isStreaming || isLoading

      return (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggle}
          disabled={isLoading}
          aria-expanded={isExpanded}
          aria-label={
            isStreaming
              ? 'AI reasoning in progress'
              : isExpanded
                ? 'Hide reasoning steps'
                : `Show ${totalStepCount} reasoning steps`
          }
          className={cn(
            'h-8 px-3 text-xs font-medium transition-colors',
            isExpanded
              ? 'text-blue-700 bg-blue-50 hover:bg-blue-100'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
          )}
        >
          <div className="flex items-center gap-2">
            {/* Icon */}
            {showSpinner ? (
              <FiActivity className="w-3 h-3 animate-spin" />
            ) : isExpanded ? (
              <FiEyeOff className="w-3 h-3" />
            ) : (
              <FiEye className="w-3 h-3" />
            )}

            {/* Label */}
            <span>
              {isStreaming
                ? 'Reasoning...'
                : isExpanded
                  ? 'Hide Reasoning'
                  : `Show Reasoning (${totalStepCount})`}
            </span>

            {/* Chevron */}
            {hasSteps &&
              !showSpinner &&
              (isExpanded ? (
                <FiChevronDown className="w-3 h-3" />
              ) : (
                <FiChevronRight className="w-3 h-3" />
              ))}
          </div>
        </Button>
      )
    }

    const renderContent = () => {
      if (!isExpanded) return null

      if (error) {
        return (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-sm text-red-600">
              Failed to load reasoning steps: {error}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchSteps}
              className="mt-2 h-7 text-xs text-red-600 hover:text-red-700"
            >
              Try Again
            </Button>
          </div>
        )
      }

      if (isStreaming && liveSteps.length === 0) {
        return (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-blue-600">
              <FiActivity className="w-4 h-4 animate-spin" />
              <span>AI is thinking...</span>
            </div>
          </div>
        )
      }

      return (
        <div className="mt-3" role="region" aria-label="AI reasoning steps">
          <ExecutionStepViewer
            steps={allSteps}
            isLoading={isLoading}
            className="bg-gray-50 border border-gray-200 rounded-lg p-4"
          />
        </div>
      )
    }

    return (
      <div className={cn('border-t border-gray-100 pt-3', className)}>
        {/* Toggle Button */}
        <div className="flex items-center justify-between">
          {renderToggleButton()}

          {/* Timestamp (if expanded and not streaming) */}
          {isExpanded && !isStreaming && isLoaded && (
            <div className="text-xs text-gray-400 flex items-center gap-1">
              <FiClock className="w-3 h-3" />
              <span>Last updated: {new Date().toLocaleTimeString()}</span>
            </div>
          )}
        </div>

        {/* Content */}
        {renderContent()}
      </div>
    )
  },
  (prevProps, nextProps) => {
    // Custom comparison function for memo optimization
    return (
      prevProps.messageId === nextProps.messageId &&
      prevProps.stepCount === nextProps.stepCount &&
      prevProps.isStreaming === nextProps.isStreaming &&
      (prevProps.liveSteps?.length ?? 0) ===
        (nextProps.liveSteps?.length ?? 0) &&
      prevProps.className === nextProps.className
    )
  }
)

export default ReasoningTimeline
