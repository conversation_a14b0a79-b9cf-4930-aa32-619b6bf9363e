'use client'

import { useState, useCallback, useRef, useEffect, useMemo } from 'react'
import { useChat } from 'ai/react'
import { nanoid } from 'nanoid'
import {
  getCachedMessages,
  setCachedMessages,
  type CachedMessage,
} from '@/app/libs/conversationCache'

type ConversationMessage = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: string
  stepCount?: number
  isStreaming?: boolean
}

type ConversationData = {
  id: string
  title?: string
  contextEntityType?: string
  contextEntityId?: string
  createdAt: string
  userId: string
  messages: ConversationMessage[]
}

type PaginationInfo = {
  hasMore: boolean
  nextCursor?: string
  limit: number
  total: number
}

type UseAiConversationOptions = {
  // Existing conversation ID to load
  conversationId?: string
  // API endpoint for chat
  apiEndpoint: string
  // Model and settings
  model?: string
  context?: string
  settings?: any
  // Pagination settings
  initialLimit?: number
  // Context for new conversations
  contextEntityType?: string
  contextEntityId?: string
}

type UseAiConversationReturn = {
  // Conversation data
  conversation: ConversationData | null
  isLoadingConversation: boolean
  conversationError: string | null

  // Messages with pagination
  messages: ConversationMessage[]
  hasMoreMessages: boolean
  isLoadingMoreMessages: boolean
  loadMoreMessages: () => Promise<void>

  // Chat functionality (from useChat)
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  handleSubmit: (e: React.FormEvent) => void
  isLoading: boolean
  append: (message: { role: 'user' | 'assistant'; content: string }) => void

  // Utilities
  refreshConversation: () => Promise<void>
  scrollToMessage: (messageId: string) => void
}

export function useAiConversation(
  options: UseAiConversationOptions
): UseAiConversationReturn {
  const {
    conversationId: initialConversationId,
    apiEndpoint,
    model = 'gpt-4.1',
    context,
    settings = {},
    initialLimit = 50,
    contextEntityType,
    contextEntityId,
  } = options

  // Conversation state
  const [conversation, setConversation] = useState<ConversationData | null>(
    null
  )
  const [isLoadingConversation, setIsLoadingConversation] =
    useState<boolean>(false)
  const [conversationError, setConversationError] = useState<string | null>(
    null
  )

  // Pagination state
  const [paginationInfo, setPaginationInfo] = useState<PaginationInfo>({
    hasMore: false,
    limit: initialLimit,
    total: 0,
  })
  const [isLoadingMoreMessages, setIsLoadingMoreMessages] =
    useState<boolean>(false)

  // Persisted messages from API (older messages)
  const [persistedMessages, setPersistedMessages] = useState<
    ConversationMessage[]
  >(() => {
    if (initialConversationId) {
      const cached = getCachedMessages(initialConversationId)
      if (cached) {
        return cached as any
      }
    }
    return []
  })

  // Current conversation ID
  const [conversationId, setConversationId] = useState<string | undefined>(
    initialConversationId
  )

  // Use the existing useChat hook for streaming new messages
  // IMPORTANT: We must keep the chatKey stable across conversationId updates
  // (e.g. after async creation) otherwise the useChat hook will remount and
  // lose the current streaming state – resulting in an empty UI until the
  // backend finishes. We therefore generate the key ONCE and store it in a
  // ref. We still differentiate simulator vs real endpoint to force a
  // remount when the user toggles that setting.
  const chatKeyRef = useRef<string>(
    `chat-${nanoid()}-${apiEndpoint.includes('chat-simulator') ? 'sim' : 'real'}`
  )

  const chatKey = chatKeyRef.current

  const chat = useChat({
    api: apiEndpoint,
    id: chatKey,
    experimental_throttle: 50,
    body: {
      model,
      context,
      settings,
      conversationId, // Pass conversation ID for persistence
      contextEntityType,
      contextEntityId,
    },
  })

  // Load conversation data
  const loadConversation = useCallback(
    async (convId: string, limit: number = initialLimit, cursor?: string) => {
      if (!convId || !convId.startsWith('thread_')) {
        console.warn('🔍 [useAiConversation] Invalid conversation ID:', convId)
        return
      }

      setIsLoadingConversation(!cursor) // Only show loading for initial load
      setIsLoadingMoreMessages(!!cursor) // Show "loading more" for pagination
      setConversationError(null)

      try {
        console.log(
          `🔍 [useAiConversation] Loading conversation: ${convId}, limit: ${limit}, cursor: ${cursor}`
        )

        const url = new URL(
          `/api/aipane/conversations/${convId}`,
          window.location.origin
        )
        url.searchParams.set('limit', limit.toString())
        url.searchParams.set('includeSteps', 'true') // load execution steps for historical msgs
        if (cursor) {
          url.searchParams.set('cursor', cursor)
        }

        const response = await fetch(url.toString())

        if (!response.ok) {
          throw new Error(`Failed to load conversation: ${response.status}`)
        }

        const data = await response.json()

        // Messages from API are ordered DESC (newest first). Reverse them to get
        // chronological order (oldest first) for correct display in the UI.
        const receivedMessages = (data.messages || [])
          .reverse()
          .map((m: any) => ({ ...m, role: (m.role || '').toLowerCase() }))

        // Debug logging for step data
        console.log(
          `🔍 [useAiConversation] Received ${receivedMessages.length} messages:`
        )
        receivedMessages.forEach((msg: any, index: number) => {
          if (msg.role === 'assistant' && msg.stepCount > 0) {
            console.log(`  Message ${index} (${msg.id}):`, {
              stepCount: msg.stepCount,
              hasSteps: !!msg.steps,
              stepsLength: msg.steps?.length,
              steps: msg.steps,
            })
          }
        })

        if (!cursor) {
          // Initial load - set conversation and messages
          setConversation(data.conversation)
          setPersistedMessages(receivedMessages)
        } else {
          // Pagination load - prepend older messages
          setPersistedMessages(prev => [...receivedMessages, ...prev])
        }

        // Cache latest messages
        const allMsgs = !cursor
          ? receivedMessages
          : [...receivedMessages, ...persistedMessages]
        if (convId) {
          setCachedMessages(convId, allMsgs as unknown as CachedMessage[])
        }

        setPaginationInfo(
          data.pagination || {
            hasMore: false,
            limit: initialLimit,
            total: 0,
          }
        )

        console.log(
          `✅ [useAiConversation] Loaded ${data.messages?.length || 0} messages`
        )
      } catch (error) {
        console.error('Error loading conversation:', error)
        setConversationError(
          error instanceof Error ? error.message : 'Unknown error'
        )
      } finally {
        setIsLoadingConversation(false)
        setIsLoadingMoreMessages(false)
      }
    },
    [initialLimit]
  )

  // Load more messages (pagination)
  const loadMoreMessages = useCallback(async () => {
    if (!conversationId || !paginationInfo.hasMore || isLoadingMoreMessages)
      return

    await loadConversation(
      conversationId,
      paginationInfo.limit,
      paginationInfo.nextCursor
    )
  }, [
    conversationId,
    paginationInfo.hasMore,
    paginationInfo.limit,
    paginationInfo.nextCursor,
    isLoadingMoreMessages,
    loadConversation,
  ])

  // Refresh conversation
  const refreshConversation = useCallback(async () => {
    if (!conversationId) return
    await loadConversation(conversationId)
  }, [conversationId, loadConversation])

  // Initialize conversation loading
  useEffect(() => {
    if (initialConversationId && initialConversationId.startsWith('thread_')) {
      console.log(
        '🔍 [useAiConversation] Initializing conversation:',
        initialConversationId
      )
      setConversationId(initialConversationId)
      loadConversation(initialConversationId)
    } else if (initialConversationId) {
      console.warn(
        '🔍 [useAiConversation] Invalid initial conversation ID:',
        initialConversationId
      )
    }
  }, [initialConversationId, loadConversation])

  // If the parent supplies a conversationId later (e.g. after a fetch),
  // update internal state and SWR key.
  useEffect(() => {
    if (
      initialConversationId &&
      initialConversationId !== conversationId &&
      initialConversationId.startsWith('thread_')
    ) {
      setConversationId(initialConversationId)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialConversationId])

  // Watch for new conversation ID from chat API responses
  useEffect(() => {
    // Extract conversation ID from chat response headers or data
    if (chat.messages.length > 0 && !conversationId) {
      // Try to extract conversation ID from the latest response
      // The chat API should include conversation ID in response
      const latestMessage = chat.messages[chat.messages.length - 1]
      if (latestMessage.role === 'assistant') {
        // Check if we can extract conversation ID from message metadata or response
        // For now, we'll rely on the parent component to set the conversation ID
        console.log(
          '🔍 [useAiConversation] New assistant message without conversation ID'
        )
      }
    }
  }, [chat.messages, conversationId])

  // Combine persisted messages (older) with streaming messages (new)
  const messages: ConversationMessage[] = useMemo(() => {
    // Merge persisted and chat.messages, avoiding duplicates by ID
    const persistedIds = new Set(persistedMessages.map(m => m.id))
    const newStreamingMessages: ConversationMessage[] = chat.messages
      .filter(m => !persistedIds.has(m.id))
      .map(m => ({
        id: m.id,
        role: (m.role || '').toLowerCase() as 'user' | 'assistant' | 'system',
        content: m.content,
        createdAt: new Date().toISOString(),
      }))

    return [...persistedMessages, ...newStreamingMessages].filter(
      m => (m.role || '').toLowerCase() !== 'system'
    )
  }, [persistedMessages, chat.messages])

  // Side-effect: whenever merged messages change, update cache
  useEffect(() => {
    if (conversationId && messages.length > 0) {
      setCachedMessages(conversationId, messages as unknown as CachedMessage[])
    }
  }, [conversationId, messages])

  // Update isStreaming flag for the last assistant message
  const messagesWithStreamingFlag = useMemo(() => {
    if (messages.length === 0) return messages

    return messages.map((message, index) => {
      const isLastMessage = index === messages.length - 1
      const isAssistantMessage = message.role === 'assistant'
      const isCurrentlyStreaming =
        chat.isLoading && isLastMessage && isAssistantMessage

      return {
        ...message,
        isStreaming: isCurrentlyStreaming,
      }
    })
  }, [messages, chat.isLoading])

  // Scroll to message utility
  const scrollToMessage = useCallback((messageId: string) => {
    const element = document.getElementById(`message-${messageId}`)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, [])

  return {
    conversation,
    isLoadingConversation,
    conversationError,
    messages: messagesWithStreamingFlag,
    hasMoreMessages: paginationInfo.hasMore,
    isLoadingMoreMessages,
    loadMoreMessages,
    input: chat.input,
    handleInputChange: chat.handleInputChange,
    handleSubmit: chat.handleSubmit,
    isLoading: chat.isLoading,
    append: chat.append,
    refreshConversation,
    scrollToMessage,
  }
}
