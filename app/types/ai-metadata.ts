/**
 * Enhanced metadata types for AI chat system
 * Provides structured metadata handling for conversations, messages, and execution steps
 */

import type { AiStepType } from '@prisma/client'

// ============================================================================
// CONVERSATION METADATA
// ============================================================================

/**
 * Metadata specific to AI conversations
 */
export type AiConversationMetadata = {
  // Model configuration
  model?: string
  temperature?: number
  maxTokens?: number
  
  // Context information
  contextSummary?: string
  contextItemCount?: number
  
  // Performance metrics
  totalSteps?: number
  totalToolCalls?: number
  averageResponseTime?: number
  
  // User preferences
  showReasoningSteps?: boolean
  autoScroll?: boolean
  
  // Custom fields for extensibility
  [key: string]: unknown
}

// ============================================================================
// MESSAGE METADATA
// ============================================================================

/**
 * Metadata specific to AI messages
 */
export type AiMessageMetadata = {
  // Processing information
  processingTime?: number
  tokenCount?: {
    input?: number
    output?: number
    total?: number
  }
  
  // Content analysis
  contentType?: 'text' | 'code' | 'mixed'
  language?: string
  complexity?: 'low' | 'medium' | 'high'
  
  // UI state
  isCollapsed?: boolean
  userRating?: 1 | 2 | 3 | 4 | 5
  
  // Reasoning-specific metadata (only for reasoning models)
  reasoning?: {
    summary?: string
    confidence?: number
    alternativeApproaches?: string[]
    keyInsights?: string[]
  }
  
  // Custom fields for extensibility
  [key: string]: unknown
}

// ============================================================================
// EXECUTION STEP METADATA
// ============================================================================

/**
 * Base metadata for all execution steps
 */
export type BaseStepMetadata = {
  timestamp: number
  duration?: number
  confidence?: number
  [key: string]: unknown
}

/**
 * Metadata for THOUGHT steps
 */
export type ThoughtStepMetadata = BaseStepMetadata & {
  reasoning: string
  thinkingTime?: number
  complexity?: 'simple' | 'moderate' | 'complex'
  category?: 'analysis' | 'planning' | 'evaluation' | 'synthesis'
}

/**
 * Metadata for TOOL_CALL steps
 */
export type ToolCallStepMetadata = BaseStepMetadata & {
  toolName: string
  args: Record<string, any>
  expectedResultType?: string
  priority?: 'low' | 'medium' | 'high'
  retryCount?: number
}

/**
 * Metadata for TOOL_RESULT steps
 */
export type ToolResultStepMetadata = BaseStepMetadata & {
  toolName: string
  result?: any
  error?: string
  resultSize?: number
  resultType?: string
  success: boolean
  // Enhanced context information
  contextProvided?: {
    summary?: string
    keyPoints?: string[]
    relevanceScore?: number
    sourceCount?: number
  }
}

/**
 * Metadata for REASONING_SUMMARY steps
 */
export type ReasoningSummaryStepMetadata = BaseStepMetadata & {
  summary: string
  keyDecisions?: string[]
  alternativesConsidered?: string[]
  confidenceLevel?: number
  reasoningChain?: string[]
}

/**
 * Metadata for SUB_AGENT_INVOCATION steps
 */
export type SubAgentStepMetadata = BaseStepMetadata & {
  agentName: string
  task: string
  agentType?: string
  expectedOutput?: string
  priority?: 'low' | 'medium' | 'high'
}

/**
 * Union type for all step metadata types
 */
export type StepMetadata = 
  | ThoughtStepMetadata
  | ToolCallStepMetadata
  | ToolResultStepMetadata
  | ReasoningSummaryStepMetadata
  | SubAgentStepMetadata

// ============================================================================
// TYPE GUARDS AND UTILITIES
// ============================================================================

/**
 * Type guard to check if metadata is for a specific step type
 */
export function isStepMetadata<T extends StepMetadata>(
  metadata: any,
  stepType: AiStepType
): metadata is T {
  if (!metadata || typeof metadata !== 'object') return false
  
  switch (stepType) {
    case 'THOUGHT':
      return typeof metadata.reasoning === 'string'
    case 'TOOL_CALL':
      return typeof metadata.toolName === 'string' && typeof metadata.args === 'object'
    case 'TOOL_RESULT':
      return typeof metadata.toolName === 'string' && typeof metadata.success === 'boolean'
    case 'REASONING_SUMMARY':
      return typeof metadata.summary === 'string'
    case 'SUB_AGENT_INVOCATION':
      return typeof metadata.agentName === 'string' && typeof metadata.task === 'string'
    default:
      return false
  }
}

/**
 * Safely extract typed metadata for a specific step type
 */
export function getTypedStepMetadata<T extends StepMetadata>(
  metadata: any,
  stepType: AiStepType
): T | null {
  return isStepMetadata<T>(metadata, stepType) ? metadata : null
}

/**
 * Create default metadata for a step type
 */
export function createDefaultStepMetadata(stepType: AiStepType): Partial<StepMetadata> {
  const base: BaseStepMetadata = {
    timestamp: Date.now()
  }
  
  switch (stepType) {
    case 'THOUGHT':
      return { ...base, reasoning: '' } as Partial<ThoughtStepMetadata>
    case 'TOOL_CALL':
      return { ...base, toolName: '', args: {} } as Partial<ToolCallStepMetadata>
    case 'TOOL_RESULT':
      return { ...base, toolName: '', success: false } as Partial<ToolResultStepMetadata>
    case 'REASONING_SUMMARY':
      return { ...base, summary: '' } as Partial<ReasoningSummaryStepMetadata>
    case 'SUB_AGENT_INVOCATION':
      return { ...base, agentName: '', task: '' } as Partial<SubAgentStepMetadata>
    default:
      return base
  }
}

// ============================================================================
// ENHANCED TOOL RESULT CONTEXT
// ============================================================================

/**
 * Enhanced tool result context for better transparency
 */
export type ToolResultContext = {
  // What information was retrieved
  contentSummary: string
  keyInsights: string[]
  
  // Source information
  sourceCount: number
  sourceTypes: string[]
  
  // Relevance and quality metrics
  relevanceScore: number // 0-1
  qualityScore: number // 0-1
  
  // What the AI learned from this
  aiTakeaways: string[]
  influenceOnResponse: string
  
  // Raw data reference (for debugging)
  rawDataSize: number
  rawDataType: string
}

/**
 * Create enhanced tool result metadata with context
 */
export function createEnhancedToolResult(
  toolName: string,
  result: any,
  context: Partial<ToolResultContext> = {}
): ToolResultStepMetadata {
  return {
    timestamp: Date.now(),
    toolName,
    result,
    success: !result?.error,
    error: result?.error,
    resultType: typeof result,
    resultSize: JSON.stringify(result).length,
    contextProvided: {
      summary: context.contentSummary || 'Information retrieved successfully',
      keyPoints: context.keyInsights || [],
      relevanceScore: context.relevanceScore || 0.8,
      sourceCount: context.sourceCount || 1,
    }
  }
}
